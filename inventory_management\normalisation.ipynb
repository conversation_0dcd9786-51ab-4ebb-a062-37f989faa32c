{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import re\n", "import pandas as pd\n", "\n", "df = pd.read_csv('inventory(in).csv', encoding='latin-1')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeableNote: you may need to restart the kernel to use updated packages.\n", "\n", "Requirement already satisfied: ace_tools in c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages (0.0)\n"]}], "source": ["pip install ace_tools"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Aggregated Inventory Data:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Inventory Balance</th>\n", "      <th>Sum of Quantity On Hand</th>\n", "      <th>_Average Cost</th>\n", "      <th>Sum of Latest Cost</th>\n", "      <th>Sum of Quantity On Order</th>\n", "      <th>Sum of T3M Qty Used</th>\n", "      <th>Sum of T6M Qty Used</th>\n", "      <th>Sum of TTM Qty Used</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>000046378R</td>\n", "      <td>906.0</td>\n", "      <td>1.0</td>\n", "      <td>906.0</td>\n", "      <td>837.300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>001300817</td>\n", "      <td>340.0</td>\n", "      <td>2.0</td>\n", "      <td>170.0</td>\n", "      <td>120.230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>001301673</td>\n", "      <td>15.0</td>\n", "      <td>1.0</td>\n", "      <td>15.0</td>\n", "      <td>15.010</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>001908R003C</td>\n", "      <td>629.0</td>\n", "      <td>2.0</td>\n", "      <td>314.0</td>\n", "      <td>286.584</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>001921130</td>\n", "      <td>5.0</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>1.790</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26409</th>\n", "      <td>PME</td>\n", "      <td>31 PM Tulsa</td>\n", "      <td>WHP34X9SVL</td>\n", "      <td>391.0</td>\n", "      <td>30.0</td>\n", "      <td>13.0</td>\n", "      <td>13.040</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>32.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26410</th>\n", "      <td>PME</td>\n", "      <td>31 PM Tulsa</td>\n", "      <td>WT25</td>\n", "      <td>175.0</td>\n", "      <td>55.0</td>\n", "      <td>3.0</td>\n", "      <td>3.490</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26411</th>\n", "      <td>PME</td>\n", "      <td>31 PM Tulsa</td>\n", "      <td>ZP00MEDH00</td>\n", "      <td>2279.0</td>\n", "      <td>1.0</td>\n", "      <td>2279.0</td>\n", "      <td>2279.240</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26412</th>\n", "      <td>PME</td>\n", "      <td>31 PM Tulsa</td>\n", "      <td>ZP00SWINLD</td>\n", "      <td>90.0</td>\n", "      <td>1.0</td>\n", "      <td>90.0</td>\n", "      <td>90.440</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26413</th>\n", "      <td>PME</td>\n", "      <td>31 PM Tulsa</td>\n", "      <td>ZP00SWINSS</td>\n", "      <td>558.0</td>\n", "      <td>1.0</td>\n", "      <td>558.0</td>\n", "      <td>558.000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>26414 rows × 11 columns</p>\n", "</div>"], "text/plain": ["      Entity       Branch MFGPARTNBR_NORMALIZED  Inventory Balance  \\\n", "0        COC      Cochise            000046378R              906.0   \n", "1        COC      Cochise             001300817              340.0   \n", "2        COC      Cochise             001301673               15.0   \n", "3        COC      Cochise           001908R003C              629.0   \n", "4        COC      Cochise             001921130                5.0   \n", "...      ...          ...                   ...                ...   \n", "26409    PME  31 PM Tulsa            WHP34X9SVL              391.0   \n", "26410    PME  31 PM Tulsa                  WT25              175.0   \n", "26411    PME  31 PM Tulsa            ZP00MEDH00             2279.0   \n", "26412    PME  31 PM Tulsa            ZP00SWINLD               90.0   \n", "26413    PME  31 PM Tulsa            ZP00SWINSS              558.0   \n", "\n", "       Sum of Quantity On Hand  _Average Cost  Sum of Latest Cost  \\\n", "0                          1.0          906.0             837.300   \n", "1                          2.0          170.0             120.230   \n", "2                          1.0           15.0              15.010   \n", "3                          2.0          314.0             286.584   \n", "4                          3.0            2.0               1.790   \n", "...                        ...            ...                 ...   \n", "26409                     30.0           13.0              13.040   \n", "26410                     55.0            3.0               3.490   \n", "26411                      1.0         2279.0            2279.240   \n", "26412                      1.0           90.0              90.440   \n", "26413                      1.0          558.0             558.000   \n", "\n", "       Sum of Quantity On Order  Sum of T3M Qty Used  Sum of T6M Qty Used  \\\n", "0                           0.0                  0.0                  0.0   \n", "1                           0.0                  0.0                  0.0   \n", "2                           0.0                  0.0                  0.0   \n", "3                           0.0                  0.0                  0.0   \n", "4                           0.0                  0.0                  0.0   \n", "...                         ...                  ...                  ...   \n", "26409                       0.0                  0.0                  0.0   \n", "26410                       0.0                  1.0                  2.0   \n", "26411                       0.0                  0.0                  0.0   \n", "26412                       0.0                  0.0                  0.0   \n", "26413                       0.0                  0.0                  0.0   \n", "\n", "       Sum of TTM Qty Used  \n", "0                      0.0  \n", "1                      0.0  \n", "2                      0.0  \n", "3                      0.0  \n", "4                     11.0  \n", "...                    ...  \n", "26409                 32.0  \n", "26410                  7.0  \n", "26411                  0.0  \n", "26412                  0.0  \n", "26413                  0.0  \n", "\n", "[26414 rows x 11 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Original unique parts: 15606\n", "Normalized unique parts: 14480\n", "Aggregation reduction: 1126\n"]}], "source": ["# Backup original for comparison later\n", "df_original = df.copy()\n", "\n", "# Step 1: Normalize MFGPARTNBR\n", "def normalize_part_number(part):\n", "    if pd.isnull(part):\n", "        return np.nan\n", "    part = part.upper()\n", "    part = part.strip()\n", "    part = re.sub(r'[^A-Z0-9]', '', part)  # remove non-alphanumeric characters\n", "    return part\n", "\n", "df['MFGPARTNBR_NORMALIZED'] = df['MFGPARTNBR'].apply(normalize_part_number)\n", "\n", "# Step 2: Clean 'Inventory Balance' which is a string like \"$2,005    \"\n", "df['Inventory Balance'] = df['Inventory Balance'].replace('[\\$,]', '', regex=True).astype(str).str.strip()\n", "df['Inventory Balance'] = pd.to_numeric(df['Inventory Balance'], errors='coerce')\n", "\n", "# Step 3: Aggregate by <PERSON><PERSON><PERSON>, Branch, and Normalized MFGPARTNBR\n", "agg_cols = [\n", "    'Inventory Balance',\n", "    'Sum of Quantity On Hand',\n", "    '_Average Cost',\n", "    'Sum of Latest Cost',\n", "    'Sum of Quantity On Order',\n", "    'Sum of T3M Qty Used',\n", "    'Sum of T6M Qty Used',\n", "    'Sum of TTM Qty Used'\n", "]\n", "\n", "df_aggregated = df.groupby(['Entity', 'Branch', 'MFGPARTNBR_NORMALIZED'])[agg_cols].sum().reset_index()\n", "\n", "# Count before and after normalization\n", "original_unique_parts = df['MFGPARTNBR'].nunique(dropna=True)\n", "normalized_unique_parts = df['MFGPARTNBR_NORMALIZED'].nunique(dropna=True)\n", "aggregation_reduction = original_unique_parts - normalized_unique_parts\n", "\n", "# Display the aggregated dataframe\n", "print(\"\\nAggregated Inventory Data:\")\n", "display(df_aggregated)\n", "\n", "print(f\"\\nOriginal unique parts: {original_unique_parts}\")\n", "print(f\"Normalized unique parts: {normalized_unique_parts}\")\n", "print(f\"Aggregation reduction: {aggregation_reduction}\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Rows that have been aggregated (showing first 10 groups):\n", "\n", "Normalized Part Number: \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7099</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>19 Springfield</td>\n", "      <td>744.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7100</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>11 Texarkana</td>\n", "      <td>970.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7101</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>1164.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7102</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>07 Houston</td>\n", "      <td>2037.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11906</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>74.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11907</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>07 Houston</td>\n", "      <td>223.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13161</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>68.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13638</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>11 Texarkana</td>\n", "      <td>67.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13639</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>18 Austin</td>\n", "      <td>67.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13640</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>Corporate</td>\n", "      <td>133.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>133.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14836</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>05 Amarillo</td>\n", "      <td>2403.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7105</th>\n", "      <td>..</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>11 Texarkana</td>\n", "      <td>94.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7106</th>\n", "      <td>..</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>1504.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14445</th>\n", "      <td>..</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>224.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14879</th>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>1556.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "7099           .                          DHU  19 Springfield   \n", "7100           .                          DHU    11 Texarkana   \n", "7101           .                          DHU  02 Albuquerque   \n", "7102           .                          DHU      07 Houston   \n", "11906          .                          DHU    10 Arlington   \n", "11907          .                          DHU      07 Houston   \n", "13161          .                          DHU    10 Arlington   \n", "13638          .                          DHU    11 Texarkana   \n", "13639          .                          DHU       18 Austin   \n", "13640          .                          DHU       Corporate   \n", "13641          .                          DHU    10 Arlington   \n", "14836          .                          DHU     05 Amarillo   \n", "7105          ..                          DHU    11 Texarkana   \n", "7106          ..                          DHU    10 Arlington   \n", "14445         ..                          DHU    10 Arlington   \n", "14879        ...                          DHU  02 Albuquerque   \n", "\n", "       Inventory Balance  \n", "7099               744.0  \n", "7100               970.0  \n", "7101              1164.0  \n", "7102              2037.0  \n", "11906               74.0  \n", "11907              223.0  \n", "13161               68.0  \n", "13638               67.0  \n", "13639               67.0  \n", "13640              133.0  \n", "13641              133.0  \n", "14836             2403.0  \n", "7105                94.0  \n", "7106              1504.0  \n", "14445              224.0  \n", "14879             1556.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 001063019VE\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5845</th>\n", "      <td>001-063-019VE</td>\n", "      <td>001063019VE</td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>2014.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5846</th>\n", "      <td>001-063-019VE</td>\n", "      <td>001063019VE</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>14194.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5844</th>\n", "      <td>001-063-019VE.</td>\n", "      <td>001063019VE</td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>15197.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "5845   001-063-019VE           001063019VE    DHU  02 Albuquerque   \n", "5846   001-063-019VE           001063019VE    DHU      06 Lubbock   \n", "5844  001-063-019VE.           001063019VE    DHU    10 Arlington   \n", "\n", "      Inventory Balance  \n", "5845             2014.0  \n", "5846            14194.0  \n", "5844            15197.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 001300954\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4654</th>\n", "      <td>001-300954</td>\n", "      <td>001300954</td>\n", "      <td>DCC</td>\n", "      <td>Double Check</td>\n", "      <td>106.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5291</th>\n", "      <td>001-300954</td>\n", "      <td>001300954</td>\n", "      <td>DHU</td>\n", "      <td>19 Springfield</td>\n", "      <td>75.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5292</th>\n", "      <td>001-300954</td>\n", "      <td>001300954</td>\n", "      <td>DHU</td>\n", "      <td>03 Midland</td>\n", "      <td>188.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "4654  001-300954             001300954    DCC    Double Check   \n", "5291  001-300954             001300954    DHU  19 Springfield   \n", "5292  001-300954             001300954    DHU      03 Midland   \n", "\n", "      Inventory Balance  \n", "4654              106.0  \n", "5291               75.0  \n", "5292              188.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 001921130\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5293</th>\n", "      <td>001-921130</td>\n", "      <td>001921130</td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>001-921130-</td>\n", "      <td>001921130</td>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "5293   001-921130             001921130    DHU  02 Albuquerque   \n", "10    001-921130-             001921130    COC         Cochise   \n", "\n", "      Inventory Balance  \n", "5293                3.0  \n", "10                  5.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0029900040\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4871</th>\n", "      <td>002990-004-0</td>\n", "      <td>0029900040</td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>41.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4872</th>\n", "      <td>002990-004-0</td>\n", "      <td>0029900040</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>277.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15856</th>\n", "      <td>002990-004-0</td>\n", "      <td>0029900040</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity        Branch  \\\n", "4871   002990-004-0            0029900040    DHU  10 Arlington   \n", "4872   002990-004-0            0029900040    DHU    06 Lubbock   \n", "15856  002990-004-0            0029900040    KUB         Kubat   \n", "\n", "       Inventory Balance  \n", "4871                41.0  \n", "4872               277.0  \n", "15856                5.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0029900330\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>002990-033-0</td>\n", "      <td>0029900330</td>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>2299.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15857</th>\n", "      <td>002990-033-0</td>\n", "      <td>0029900330</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>296.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity   Branch  Inventory Balance\n", "13     002990-033-0            0029900330    COC  Cochise             2299.0\n", "15857  002990-033-0            0029900330    KUB    Kubat              296.0"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 004921469KIT\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4661</th>\n", "      <td>004-921469-K<PERSON></td>\n", "      <td>004921469KIT</td>\n", "      <td>DCC</td>\n", "      <td>Double Check</td>\n", "      <td>173.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5298</th>\n", "      <td>004-921469-K<PERSON></td>\n", "      <td>004921469KIT</td>\n", "      <td>DHU</td>\n", "      <td>03 Midland</td>\n", "      <td>679.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15145</th>\n", "      <td>004-921469-K<PERSON></td>\n", "      <td>004921469KIT</td>\n", "      <td>HCN</td>\n", "      <td>37 HCN</td>\n", "      <td>226.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           MFGPARTNBR MFGPARTNBR_NORMALIZED Entity        Branch  \\\n", "4661   004-921469-KIT          004921469KIT    DCC  Double Check   \n", "5298   004-921469-KIT          004921469KIT    DHU    03 Midland   \n", "15145  004-921469-KIT          004921469KIT    HCN        37 HCN   \n", "\n", "       Inventory Balance  \n", "4661               173.0  \n", "5298               679.0  \n", "15145              226.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0120200045\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4877</th>\n", "      <td>012020-004-5</td>\n", "      <td>0120200045</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>144.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15901</th>\n", "      <td>012020-004-5</td>\n", "      <td>0120200045</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>245.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity      Branch  \\\n", "4877   012020-004-5            0120200045    DHU  06 Lubbock   \n", "15901  012020-004-5            0120200045    KUB       Kubat   \n", "\n", "       Inventory Balance  \n", "4877               144.0  \n", "15901              245.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0120201018\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4878</th>\n", "      <td>012020-101-8</td>\n", "      <td>0120201018</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>1515.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4879</th>\n", "      <td>012020-101-8</td>\n", "      <td>0120201018</td>\n", "      <td>DHU</td>\n", "      <td>09 Schertz</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15902</th>\n", "      <td>012020-101-8</td>\n", "      <td>0120201018</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1624.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity      Branch  \\\n", "4878   012020-101-8            0120201018    DHU  06 Lubbock   \n", "4879   012020-101-8            0120201018    DHU  09 Schertz   \n", "15902  012020-101-8            0120201018    KUB       Kubat   \n", "\n", "       Inventory Balance  \n", "4878              1515.0  \n", "4879                11.0  \n", "15902             1624.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0120201914\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4880</th>\n", "      <td>012020-191-4</td>\n", "      <td>0120201914</td>\n", "      <td>DHU</td>\n", "      <td>09 Schertz</td>\n", "      <td>366.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15905</th>\n", "      <td>012020-191-4</td>\n", "      <td>0120201914</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2154.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity      Branch  \\\n", "4880   012020-191-4            0120201914    DHU  09 Schertz   \n", "15905  012020-191-4            0120201914    KUB       Kubat   \n", "\n", "       Inventory Balance  \n", "4880               366.0  \n", "15905             2154.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Find rows that have been aggregated\n", "# First, get all normalized part numbers that have duplicates\n", "duplicate_normalized = df.groupby('MFGPARTNBR_NORMALIZED').filter(lambda x: len(x) > 1)\n", "\n", "# Sort by normalized part number to see the aggregations together\n", "duplicate_normalized = duplicate_normalized.sort_values(['MFGPARTNBR_NORMALIZED', 'MFGPARTNBR'])\n", "\n", "# Display the rows that have been aggregated\n", "print(\"\\nRows that have been aggregated (showing first 10 groups):\")\n", "for norm_part in duplicate_normalized['MFGPARTNBR_NORMALIZED'].unique()[:10]:\n", "    print(f\"\\nNormalized Part Number: {norm_part}\")\n", "    group = duplicate_normalized[duplicate_normalized['MFGPARTNBR_NORMALIZED'] == norm_part]\n", "    display(group[['MFGPARTNBR', 'MFGPARTNBR_NORMALIZED', 'Entity', 'Branch', 'Inventory Balance']])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Rows that have been aggregated (showing first 10 groups):\n", "\n", "Normalized Part Number: \n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7099</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>19 Springfield</td>\n", "      <td>744.0</td>\n", "      <td>Thread <PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7100</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>11 Texarkana</td>\n", "      <td>970.0</td>\n", "      <td>Thread <PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7101</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>1164.0</td>\n", "      <td>Thread <PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7102</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>07 Houston</td>\n", "      <td>2037.0</td>\n", "      <td>Thread <PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>11906</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>74.0</td>\n", "      <td>ADAPTER, 2X2 MXF, 523 - OPW</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11907</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>07 Houston</td>\n", "      <td>223.0</td>\n", "      <td>ADAPTER, 2X2 MXF, 523 - OPW</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13161</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>68.0</td>\n", "      <td>Hose Bumper 3/4\"</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13638</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>11 Texarkana</td>\n", "      <td>67.0</td>\n", "      <td>Housing Kit, Hi Pressure Check Valve</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13639</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>18 Austin</td>\n", "      <td>67.0</td>\n", "      <td>Housing Kit, Hi Pressure Check Valve</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13640</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>Corporate</td>\n", "      <td>133.0</td>\n", "      <td>Housing Kit, Hi Pressure Check Valve</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13641</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>133.0</td>\n", "      <td>Housing Kit, Hi Pressure Check Valve</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14836</th>\n", "      <td>.</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>05 Amarillo</td>\n", "      <td>2403.0</td>\n", "      <td>8  HGP Mag1 Plus Probe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7105</th>\n", "      <td>..</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>11 Texarkana</td>\n", "      <td>94.0</td>\n", "      <td>PLS2 Gasoila 1 Pint</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7106</th>\n", "      <td>..</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>1504.0</td>\n", "      <td>PLS2 Gasoila 1 Pint</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14445</th>\n", "      <td>..</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>224.0</td>\n", "      <td>TRANSFORMER, VR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14879</th>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>1556.0</td>\n", "      <td>SS Probe</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "7099           .                          DHU  19 Springfield   \n", "7100           .                          DHU    11 Texarkana   \n", "7101           .                          DHU  02 Albuquerque   \n", "7102           .                          DHU      07 Houston   \n", "11906          .                          DHU    10 Arlington   \n", "11907          .                          DHU      07 Houston   \n", "13161          .                          DHU    10 Arlington   \n", "13638          .                          DHU    11 Texarkana   \n", "13639          .                          DHU       18 Austin   \n", "13640          .                          DHU       Corporate   \n", "13641          .                          DHU    10 Arlington   \n", "14836          .                          DHU     05 Amarillo   \n", "7105          ..                          DHU    11 Texarkana   \n", "7106          ..                          DHU    10 Arlington   \n", "14445         ..                          DHU    10 Arlington   \n", "14879        ...                          DHU  02 Albuquerque   \n", "\n", "       Inventory Balance                           DESCRIPTION  \n", "7099               744.0                        Thread Sealant  \n", "7100               970.0                        Thread Sealant  \n", "7101              1164.0                        Thread Sealant  \n", "7102              2037.0                        Thread Sealant  \n", "11906               74.0           ADAPTER, 2X2 MXF, 523 - OPW  \n", "11907              223.0           ADAPTER, 2X2 MXF, 523 - OPW  \n", "13161               68.0                      <PERSON><PERSON> Bumper 3/4\"  \n", "13638               67.0  Housing Kit, Hi Pressure Check Valve  \n", "13639               67.0  Housing Kit, Hi Pressure Check Valve  \n", "13640              133.0  Housing Kit, Hi Pressure Check Valve  \n", "13641              133.0  Housing Kit, Hi Pressure Check Valve  \n", "14836             2403.0                8  HGP Mag1 Plus Probe  \n", "7105                94.0                   PLS2 Gasoila 1 Pint  \n", "7106              1504.0                   PLS2 Gasoila 1 Pint  \n", "14445              224.0                       TRANSFORMER, VR  \n", "14879             1556.0                              SS Probe  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 001063019VE\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5845</th>\n", "      <td>001-063-019VE</td>\n", "      <td>001063019VE</td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>2014.0</td>\n", "      <td>PIPE UPP UL971 EVOH 2\" SW VVR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5846</th>\n", "      <td>001-063-019VE</td>\n", "      <td>001063019VE</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>14194.0</td>\n", "      <td>PIPE UPP UL971 EVOH 2\" SW VVR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5844</th>\n", "      <td>001-063-019VE.</td>\n", "      <td>001063019VE</td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>15197.0</td>\n", "      <td>UPP Pipe UL971 EVOH 2\"</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "5845   001-063-019VE           001063019VE    DHU  02 Albuquerque   \n", "5846   001-063-019VE           001063019VE    DHU      06 Lubbock   \n", "5844  001-063-019VE.           001063019VE    DHU    10 Arlington   \n", "\n", "      Inventory Balance                    DESCRIPTION  \n", "5845             2014.0  PIPE UPP UL971 EVOH 2\" SW VVR  \n", "5846            14194.0  PIPE UPP UL971 EVOH 2\" SW VVR  \n", "5844            15197.0         UPP Pipe UL971 EVOH 2\"  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 001300954\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4654</th>\n", "      <td>001-300954</td>\n", "      <td>001300954</td>\n", "      <td>DCC</td>\n", "      <td>Double Check</td>\n", "      <td>106.0</td>\n", "      <td>I Meter Check Valve</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5291</th>\n", "      <td>001-300954</td>\n", "      <td>001300954</td>\n", "      <td>DHU</td>\n", "      <td>19 Springfield</td>\n", "      <td>75.0</td>\n", "      <td>Check Valve W/Cage,Dw July 07 and later</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5292</th>\n", "      <td>001-300954</td>\n", "      <td>001300954</td>\n", "      <td>DHU</td>\n", "      <td>03 Midland</td>\n", "      <td>188.0</td>\n", "      <td>Check Valve W/Cage,Dw July 07 and later</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "4654  001-300954             001300954    DCC    Double Check   \n", "5291  001-300954             001300954    DHU  19 Springfield   \n", "5292  001-300954             001300954    DHU      03 Midland   \n", "\n", "      Inventory Balance                              DESCRIPTION  \n", "4654              106.0                      I Meter Check Valve  \n", "5291               75.0  Check Valve W/Cage,Dw July 07 and later  \n", "5292              188.0  Check Valve W/Cage,Dw July 07 and later  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 001921130\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5293</th>\n", "      <td>001-921130</td>\n", "      <td>001921130</td>\n", "      <td>DHU</td>\n", "      <td>02 Albuquerque</td>\n", "      <td>3.0</td>\n", "      <td><PERSON><PERSON><PERSON>,Double Bump,DW.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>001-921130-</td>\n", "      <td>001921130</td>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>5.0</td>\n", "      <td>METER DISCHARGE O-RING</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       MFGPARTNBR MFGPARTNBR_NORMALIZED Entity          Branch  \\\n", "5293   001-921130             001921130    DHU  02 Albuquerque   \n", "10    001-921130-             001921130    COC         Cochise   \n", "\n", "      Inventory Balance             DESCRIPTION  \n", "5293                3.0  <PERSON><PERSON><PERSON>,<PERSON> Bump,DW.  \n", "10                  5.0  METER DISCHARGE O-RING  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0029900040\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4871</th>\n", "      <td>002990-004-0</td>\n", "      <td>0029900040</td>\n", "      <td>DHU</td>\n", "      <td>10 Arlington</td>\n", "      <td>41.0</td>\n", "      <td>Filler For 8014 Adhesive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4872</th>\n", "      <td>002990-004-0</td>\n", "      <td>0029900040</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>277.0</td>\n", "      <td>Filler For 8014 Adhesive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15856</th>\n", "      <td>002990-004-0</td>\n", "      <td>0029900040</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>5.0</td>\n", "      <td>Filler for DS-8014</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity        Branch  \\\n", "4871   002990-004-0            0029900040    DHU  10 Arlington   \n", "4872   002990-004-0            0029900040    DHU    06 Lubbock   \n", "15856  002990-004-0            0029900040    KUB         Kubat   \n", "\n", "       Inventory Balance               DESCRIPTION  \n", "4871                41.0  Filler For 8014 Adhesive  \n", "4872               277.0  Filler For 8014 Adhesive  \n", "15856                5.0        Filler for DS-8014  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0029900330\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>002990-033-0</td>\n", "      <td>0029900330</td>\n", "      <td>COC</td>\n", "      <td>Cochise</td>\n", "      <td>2299.0</td>\n", "      <td>GLUE KIT FILLER</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15857</th>\n", "      <td>002990-033-0</td>\n", "      <td>0029900330</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>296.0</td>\n", "      <td>Filler for DS-8069</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity   Branch  Inventory Balance  \\\n", "13     002990-033-0            0029900330    COC  Cochise             2299.0   \n", "15857  002990-033-0            0029900330    KUB    Kubat              296.0   \n", "\n", "              DESCRIPTION  \n", "13        GLUE KIT FILLER  \n", "15857  Filler for DS-8069  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 004921469KIT\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4661</th>\n", "      <td>004-921469-K<PERSON></td>\n", "      <td>004921469KIT</td>\n", "      <td>DCC</td>\n", "      <td>Double Check</td>\n", "      <td>173.0</td>\n", "      <td>I Pulser Kit-Vista Ovation I</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5298</th>\n", "      <td>004-921469-K<PERSON></td>\n", "      <td>004921469KIT</td>\n", "      <td>DHU</td>\n", "      <td>03 Midland</td>\n", "      <td>679.0</td>\n", "      <td>Pulser Kit, 2 &amp; 3 Vista ,IGEM.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15145</th>\n", "      <td>004-921469-K<PERSON></td>\n", "      <td>004921469KIT</td>\n", "      <td>HCN</td>\n", "      <td>37 HCN</td>\n", "      <td>226.0</td>\n", "      <td>Pulser Kit, 2 &amp; 3 Vista ,IGEM.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           MFGPARTNBR MFGPARTNBR_NORMALIZED Entity        Branch  \\\n", "4661   004-921469-KIT          004921469KIT    DCC  Double Check   \n", "5298   004-921469-KIT          004921469KIT    DHU    03 Midland   \n", "15145  004-921469-KIT          004921469KIT    HCN        37 HCN   \n", "\n", "       Inventory Balance                     DESCRIPTION  \n", "4661               173.0    I Pulser Kit-Vista Ovation I  \n", "5298               679.0  Pulser Kit, 2 & 3 Vista ,IGEM.  \n", "15145              226.0  Pulser Kit, 2 & 3 Vista ,IGEM.  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0120200045\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4877</th>\n", "      <td>012020-004-5</td>\n", "      <td>0120200045</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>144.0</td>\n", "      <td>2-Inch x 4-Inch Fiberglass Nipple</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15901</th>\n", "      <td>012020-004-5</td>\n", "      <td>0120200045</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>245.0</td>\n", "      <td>2\" x 4\" Nipple - RTIIA Primary Fitting</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity      Branch  \\\n", "4877   012020-004-5            0120200045    DHU  06 Lubbock   \n", "15901  012020-004-5            0120200045    KUB       Kubat   \n", "\n", "       Inventory Balance                             DESCRIPTION  \n", "4877               144.0       2-Inch x 4-Inch Fiberglass Nipple  \n", "15901              245.0  2\" x 4\" Nipple - RTIIA Primary Fitting  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0120201018\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4878</th>\n", "      <td>012020-101-8</td>\n", "      <td>0120201018</td>\n", "      <td>DHU</td>\n", "      <td>06 <PERSON><PERSON>ock</td>\n", "      <td>1515.0</td>\n", "      <td>Coupling 2\"\",Ao</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4879</th>\n", "      <td>012020-101-8</td>\n", "      <td>0120201018</td>\n", "      <td>DHU</td>\n", "      <td>09 Schertz</td>\n", "      <td>11.0</td>\n", "      <td>Coupling 2\"\",Ao</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15902</th>\n", "      <td>012020-101-8</td>\n", "      <td>0120201018</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1624.0</td>\n", "      <td>2\" Sleeve Coupling - RTIIA Primary Fitting</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity      Branch  \\\n", "4878   012020-101-8            0120201018    DHU  06 Lubbock   \n", "4879   012020-101-8            0120201018    DHU  09 Schertz   \n", "15902  012020-101-8            0120201018    KUB       Kubat   \n", "\n", "       Inventory Balance                                 DESCRIPTION  \n", "4878              1515.0                             Coupling 2\"\",Ao  \n", "4879                11.0                             Coupling 2\"\",Ao  \n", "15902             1624.0  2\" Sleeve Coupling - RTIIA Primary Fitting  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Normalized Part Number: 0120201914\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MFGPARTNBR</th>\n", "      <th>MFGPARTNBR_NORMALIZED</th>\n", "      <th>Entity</th>\n", "      <th>Branch</th>\n", "      <th>Inventory Balance</th>\n", "      <th>DESCRIPTION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4880</th>\n", "      <td>012020-191-4</td>\n", "      <td>0120201914</td>\n", "      <td>DHU</td>\n", "      <td>09 Schertz</td>\n", "      <td>366.0</td>\n", "      <td>Adapt<PERSON>,2\"\",BxM,Fg,Ao</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15905</th>\n", "      <td>012020-191-4</td>\n", "      <td>0120201914</td>\n", "      <td>KUB</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>2154.0</td>\n", "      <td>2\" BxM Threaded Adapter, NPT Threads - RTIIA P...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         MFGPARTNBR MFGPARTNBR_NORMALIZED Entity      Branch  \\\n", "4880   012020-191-4            0120201914    DHU  09 Schertz   \n", "15905  012020-191-4            0120201914    KUB       Kubat   \n", "\n", "       Inventory Balance                                        DESCRIPTION  \n", "4880               366.0                              Adapter,2\"\",<PERSON>x<PERSON>,<PERSON>g,<PERSON>o  \n", "15905             2154.0  2\" BxM Threaded Adapter, NPT Threads - RTIIA P...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Find rows that have been aggregated\n", "# First, get all normalized part numbers that have duplicates\n", "duplicate_normalized = df.groupby('MFGPARTNBR_NORMALIZED').filter(lambda x: len(x) > 1)\n", "\n", "# Sort by normalized part number to see the aggregations together\n", "duplicate_normalized = duplicate_normalized.sort_values(['MFGPARTNBR_NORMALIZED', 'MFGPARTNBR'])\n", "\n", "# Display the rows that have been aggregated\n", "print(\"\\nRows that have been aggregated (showing first 10 groups):\")\n", "for norm_part in duplicate_normalized['MFGPARTNBR_NORMALIZED'].unique()[:10]:\n", "    print(f\"\\nNormalized Part Number: {norm_part}\")\n", "    group = duplicate_normalized[duplicate_normalized['MFGPARTNBR_NORMALIZED'] == norm_part]\n", "    display(group[['MFGPARTNBR', 'MFGPARTNBR_NORMALIZED', 'Entity', 'Branch', 'Inventory Balance', 'DESCRIPTION']])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}