server {
    listen 80;
    
    # Enable gzip
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
      application/javascript
      application/json
      text/css
      text/plain;
    
    # Root directory and default index
    root /usr/share/nginx/html;
    index index.html;
    
    # Main location block for SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Static asset caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }
    
    # No caching for HTML
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        expires 0;
    }
    
    # Redirect 404 to index for SPA
    error_page 404 /index.html;
    
    # Simple status page for health checks
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
    }
}