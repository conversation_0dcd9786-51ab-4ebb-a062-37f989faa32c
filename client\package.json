{"name": "zentroq-inventory-management", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^3.28.1", "@azure/msal-react": "^2.2.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "^2.0.18", "@mui/material": "^5.15.15", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.2", "react-scripts": "5.0.1", "react-select": "^5.10.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.31", "tailwindcss": "^3.3.2"}, "overrides": {"nth-check": "^2.0.1"}}