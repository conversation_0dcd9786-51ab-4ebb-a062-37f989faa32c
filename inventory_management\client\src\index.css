@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%);
  color: #111827;
}

/* Glass morphism effects */
.glassmorphism {
  @apply bg-white/80 backdrop-blur-md rounded-xl border border-gray-200 shadow-sm;
}

.glass-card {
  @apply bg-white/90 backdrop-blur-md rounded-xl border border-gray-200 shadow-sm;
}

.glass-panel {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.glass-input {
  @apply bg-white/90 backdrop-blur-md border border-gray-200 rounded-md focus:ring-2 focus:ring-gray-300 focus:border-transparent text-gray-900;
}

/* Fixed modal overlays */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999; /* Very high z-index to ensure it's above everything */
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 10000; /* Even higher z-index */
  position: relative; /* Ensure proper stacking */
}

/* Prevent scrolling when modal is open */
body.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Advanced filters styling - modified to allow search but disable entity/branch selection */
/* Only disable the branch dropdown when advanced filters are active */
.advanced-filters-active .zentroq-select__control {
  pointer-events: none !important;
  cursor: not-allowed !important;
  opacity: 0.5 !important;
  background-color: #f3f4f6 !important;
  border-color: #e5e7eb !important;
}

/* Disabled buttons in advanced filter mode */
.advanced-filters-active button:not([type="button"]):not(.advanced-filter-control),
.advanced-filters-active .filter-button {
  pointer-events: none !important;
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

/* Make sure advanced filter controls are always clickable */
.advanced-filter-control {
  pointer-events: auto !important;
  cursor: pointer !important;
  opacity: 1 !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(240, 240, 240, 0.8);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}