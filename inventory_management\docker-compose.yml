version: '3.8'

services:
  api:
    build: ./api
    container_name: inventory-api
    ports:
      - "8000:80"
    volumes:
      - ./:/app/data
      - ./api/app:/app/app
      - ./.env:/app/app/.env
    environment:
      - ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    
  client:
    build: ./client
    container_name: inventory-client
    ports:
      - "8080:80"
    depends_on:
      api:
        condition: service_healthy
    volumes:
      - ./client/nginx.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped