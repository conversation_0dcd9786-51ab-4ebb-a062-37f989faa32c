export const inventoryItems = [
  {
    id: 1,
    entity: "COC",
    branch: "Cochise",
    partNumber: "*SERVICES",
    mfgName: "MISC VENDORS",
    mfgPartNumber: "*SERVICES",
    description: "",
    family: "",
    category: "",
    inventoryBalance: 2005,
    quantityOnHand: 2,
    averageCost: 1003,
    latestCost: 360,
    quantityOnOrder: 5,
    t3mQtyUsed: 0,
    t6mQtyUsed: 0,
    ttmQtyUsed: 0,
    monthsOfCover: Infinity,
    lastReceipt: null,
    status: "dead" // Added for filtering
  },
  {
    id: 2,
    entity: "COC",
    branch: "Cochise",
    partNumber: "*WOP",
    mfgName: "MISC VENDORS",
    mfgPartNumber: "*WOP",
    description: "NON-STOCK WORK ORDER PART (PO)",
    family: "",
    category: "",
    inventoryBalance: 6849,
    quantityOnHand: 7,
    averageCost: 978,
    latestCost: 1188.195,
    quantityOnOrder: 0,
    t3mQtyUsed: 0,
    t6mQtyUsed: 0,
    ttmQtyUsed: 0,
    monthsOfCover: Infinity,
    lastReceipt: null,
    status: "dead" // No usage
  },
  {
    id: 3,
    entity: "COC",
    branch: "Cochise",
    partNumber: "000-046378-R",
    mfgName: "Dresser Wayne",
    mfgPartNumber: "000-046378-R",
    description: "WAYNE MECHANICAL METER",
    family: "SERVICE",
    category: "METER",
    inventoryBalance: 906,
    quantityOnHand: 1,
    averageCost: 906,
    latestCost: 837.3,
    quantityOnOrder: 0,
    t3mQtyUsed: 0,
    t6mQtyUsed: 0,
    ttmQtyUsed: 0,
    monthsOfCover: Infinity,
    lastReceipt: "2022-05-03",
    status: "dead" // No recent usage
  },
  {
    id: 4,
    entity: "COC",
    branch: "Cochise",
    partNumber: "87",
    mfgName: "Husky",
    mfgPartNumber: "87",
    description: "1\" SWIVEL MULTIPLANE",
    family: "FITTINGS",
    category: "SWIVEL",
    inventoryBalance: 2774,
    quantityOnHand: 62,
    averageCost: 45,
    latestCost: 57.19,
    quantityOnOrder: -1,
    t3mQtyUsed: 27,
    t6mQtyUsed: 42,
    ttmQtyUsed: 81,
    monthsOfCover: 9.18,
    lastReceipt: "2024-12-18",
    status: "excess" // High quantity vs usage
  },
  {
    id: 5,
    entity: "COC",
    branch: "Cochise",
    partNumber: "95",
    mfgName: "Husky",
    mfgPartNumber: "95",
    description: "1\" CABINET SWIVEL",
    family: "FITTINGS",
    category: "SWIVEL",
    inventoryBalance: 144,
    quantityOnHand: 6,
    averageCost: 24,
    latestCost: 28.71,
    quantityOnOrder: 0,
    t3mQtyUsed: 0,
    t6mQtyUsed: 0,
    ttmQtyUsed: 0,
    monthsOfCover: Infinity,
    lastReceipt: "2024-06-03",
    status: "dead" // No usage
  },
  {
    id: 6,
    entity: "COC", 
    branch: "Cochise",
    partNumber: "350",
    mfgName: "Husky",
    mfgPartNumber: "350",
    description: "3/4\" SWIVEL MULTIPLANE..",
    family: "FITTINGS",
    category: "SWIVEL",
    inventoryBalance: 2018,
    quantityOnHand: 79,
    averageCost: 26,
    latestCost: 19.32,
    quantityOnOrder: 0,
    t3mQtyUsed: 37,
    t6mQtyUsed: 64,
    ttmQtyUsed: 172,
    monthsOfCover: 5.51,
    lastReceipt: "2025-02-11",
    status: "optimal" // Good inventory level
  },
  {
    id: 7,
    entity: "COC",
    branch: "Cochise",
    partNumber: "385",
    mfgName: "Husky",
    mfgPartNumber: "385",
    description: "REDUCER BUSHING 1\" M X 3/4\" F",
    family: "FITTINGS",
    category: "BUSHING",
    inventoryBalance: 20,
    quantityOnHand: 6,
    averageCost: 3,
    latestCost: 3.36,
    quantityOnOrder: 0,
    t3mQtyUsed: 0,
    t6mQtyUsed: 0,
    ttmQtyUsed: 2,
    monthsOfCover: 36,
    lastReceipt: "2023-03-27",
    status: "excess" // High months of cover
  },
  {
    id: 8,
    entity: "COC",
    branch: "Cochise",
    partNumber: "001-300817-",
    mfgName: "Dresser Wayne",
    mfgPartNumber: "001-300817-",
    description: "PULSAR ASSEMBLY",
    family: "SERVICE",
    category: "PULSER",
    inventoryBalance: 340,
    quantityOnHand: 2,
    averageCost: 170,
    latestCost: 120.23,
    quantityOnOrder: 0,
    t3mQtyUsed: 1,
    t6mQtyUsed: 1,
    ttmQtyUsed: 2,
    monthsOfCover: 12,
    lastReceipt: "2023-10-11",
    status: "optimal" // Reasonable stock
  },
  {
    id: 9,
    entity: "COC",
    branch: "Cochise",
    partNumber: "001-301673",
    mfgName: "Dresser Wayne",
    mfgPartNumber: "001-301673",
    description: "SWITCH MEMBRANE",
    family: "SERVICE",
    category: "ELECTRONIC BOARDS",
    inventoryBalance: 15,
    quantityOnHand: 1,
    averageCost: 15,
    latestCost: 15.01,
    quantityOnOrder: 0,
    t3mQtyUsed: 2,
    t6mQtyUsed: 6,
    ttmQtyUsed: 12,
    monthsOfCover: 1,
    lastReceipt: "2022-04-16",
    status: "low" // Low stock
  },
  {
    id: 10,
    entity: "COC",
    branch: "Cochise",
    partNumber: "001-32435",
    mfgName: "Dresser Wayne",
    mfgPartNumber: "001-32435",
    description: "DISPLAY BOARD",
    family: "SERVICE",
    category: "ELECTRONIC BOARDS",
    inventoryBalance: 350,
    quantityOnHand: 1,
    averageCost: 350,
    latestCost: 342.50,
    quantityOnOrder: 0,
    t3mQtyUsed: 1,
    t6mQtyUsed: 3,
    ttmQtyUsed: 5,
    monthsOfCover: 2.4,
    lastReceipt: "2022-11-22",
    status: "low" // Low stock
  },
  {
    id: 11,
    entity: "PHX",
    branch: "Phoenix",
    partNumber: "87",
    mfgName: "Husky",
    mfgPartNumber: "87",
    description: "1\" SWIVEL MULTIPLANE",
    family: "FITTINGS",
    category: "SWIVEL",
    inventoryBalance: 1800,
    quantityOnHand: 40,
    averageCost: 45,
    latestCost: 57.19,
    quantityOnOrder: 0,
    t3mQtyUsed: 18,
    t6mQtyUsed: 35,
    ttmQtyUsed: 70,
    monthsOfCover: 6.85,
    lastReceipt: "2025-01-10",
    status: "optimal" // Good inventory level
  },
  {
    id: 12,
    entity: "PHX",
    branch: "Phoenix",
    partNumber: "350",
    mfgName: "Husky",
    mfgPartNumber: "350",
    description: "3/4\" SWIVEL MULTIPLANE..",
    family: "FITTINGS",
    category: "SWIVEL",
    inventoryBalance: 780,
    quantityOnHand: 30,
    averageCost: 26,
    latestCost: 19.32,
    quantityOnOrder: 15,
    t3mQtyUsed: 25,
    t6mQtyUsed: 50,
    ttmQtyUsed: 100,
    monthsOfCover: 3.6,
    lastReceipt: "2025-01-15",
    status: "optimal" // Good inventory level
  }
];

export const branches = [
  { id: 1, name: "Cochise", code: "COC" },
  { id: 2, name: "Phoenix", code: "PHX" },
  { id: 3, name: "Tucson", code: "TUC" },
  { id: 4, name: "Mesa", code: "MES" },
  { id: 5, name: "Yuma", code: "YUM" }
];

export const categories = [
  "METER",
  "PULSER",
  "ELECTRONIC BOARDS",
  "SWIVEL",
  "BUSHING",
  "OTHER"
];

export const families = [
  "SERVICE",
  "FITTINGS",
  "DISPENSERS",
  "NOZZLES",
  "ACCESSORIES"
];

export const statuses = [
  { value: "all", label: "All" },
  { value: "excess", label: "Excess" },
  { value: "optimal", label: "Optimal" },
  { value: "low", label: "Low Stock" },
  { value: "dead", label: "Dead Stock" }
];